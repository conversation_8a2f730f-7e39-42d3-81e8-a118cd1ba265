<div class="p-4 border-b border-gray-200">
  <div class="flex flex-col space-y-3 md:flex-row md:space-y-0 md:space-x-4 md:items-center">
    <!-- Search Box -->
    <div class="flex-1">
      <%= form_with url: songs_path, method: :get, local: false, data: {
        turbo_frame: "song_list_frame",
        controller: "search",
        search_target: "form"
      }, class: "flex items-center" do |form| %>
        <div class="relative w-full">
          <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <%= flowbite_icon('search-outline', class: 'w-4 h-4 text-gray-500') %>
          </div>
          <%= form.text_field :search,
            value: params[:search],
            placeholder: "Search songs...",
            class: "block w-full pl-10 pr-3 py-2 text-sm border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500",
            data: {
              search_target: "input",
              action: "input->search#search"
            } %>
          <!-- Hidden fields to preserve other parameters -->
          <%= form.hidden_field :sort, value: params[:sort] %>
          <%= form.hidden_field :order, value: params[:order] %>
          <%= form.hidden_field :favorites, value: params[:favorites] %>
          <%= form.hidden_field :page, value: 1 %>
        </div>
      <% end %>
    </div>
    <!-- Sort Dropdown -->
    <div class="relative">
      <%= render "sort_dropdown" %>
    </div>
    <!-- Favorites Toggle Button -->
    <div class="relative">
      <%= link_to songs_path(params.permit!.merge(favorites: params[:favorites] == 'true' ? nil : 'true', page: 1)),
        data: { turbo_frame: "song_list_frame" },
        class: "flex items-center px-4 py-2 text-sm font-medium rounded-lg border #{params[:favorites] == 'true' ? 'bg-red-50 text-red-700 border-red-200' : 'bg-white text-gray-900 border-gray-200'} hover:bg-gray-50 focus:outline-none focus:ring-4 focus:ring-gray-200" do %>
        <%= flowbite_icon('heart-outline', class: 'w-4 h-4 mr-2') %>
        <%= params[:favorites] == 'true' ? 'Show All' : 'Favorites Only' %>
      <% end %>
    </div>
  </div>
  <!-- Active Filters Display -->
  <% if params[:search].present? || params[:favorites] == 'true' %>
    <div class="flex flex-wrap items-center gap-2 mt-3">
      <span class="text-sm text-gray-500">Active filters:</span>
      <% if params[:search].present? %>
        <span class="inline-flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
          Search: "<%= params[:search] %>"
          <%= link_to songs_path(params.except(:search).permit!),
            data: { turbo_frame: "song_list_frame" },
            class: "ml-1 text-blue-600 hover:text-blue-800" do %>
            ×
          <% end %>
        </span>
      <% end %>
      <% if params[:favorites] == 'true' %>
        <span class="inline-flex items-center px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">
          Favorites only
          <%= link_to songs_path(params.except(:favorites).permit!),
            data: { turbo_frame: "song_list_frame" },
            class: "ml-1 text-red-600 hover:text-red-800" do %>
            ×
          <% end %>
        </span>
      <% end %>
      <!-- Clear All Filters -->
      <%= link_to "Clear all", songs_path, 
        data: { turbo_frame: "song_list_frame" },
        class: "text-sm text-gray-500 hover:text-gray-700 underline" %>
    </div>
  <% end %>
</div>
